import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
  Button,
  Fade,
  Slide,
} from '@mui/material';
import {
  Close as CloseIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  Article as ArticleIcon,
  ContactMail as ContactMailIcon,
  Star as StarIcon,
  Book as BookIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ToolpadAccountComponent from './auth/toolpad/ToolpadAccountComponent';

const ModernMobileMenu = ({
  open,
  onClose,
  isAuthenticated,
  authJsAuthenticated,
  user,
}) => {
  const navigate = useNavigate();
  const [animationDelay, setAnimationDelay] = useState(0);

  useEffect(() => {
    if (open) {
      setAnimationDelay(100);
    } else {
      setAnimationDelay(0);
    }
  }, [open]);

  const menuItems = [
    { id: 'home', label: 'Home', icon: <HomeIcon />, path: '/' },
    { id: 'hero', label: 'Hero', icon: <StarIcon />, path: '/heroref' },
    { id: 'about', label: 'About Me', icon: <PersonIcon />, path: '/about' },
    { id: 'portfolio', label: 'Portfolio', icon: <WorkIcon />, path: '/works' },
    { id: 'blog', label: 'Blog', icon: <ArticleIcon />, path: '/blogs' },
    { id: 'contact', label: 'Contact', icon: <ContactMailIcon />, path: '/contact' },
    { id: 'docs', label: 'Documentation', icon: <BookIcon />, path: '/docs' },
  ];

  const handleItemClick = (path) => {
    onClose();
    navigate(path);
  };

  const isUserAuthenticated = isAuthenticated || authJsAuthenticated;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: '100vw',
          height: '100vh',
          background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',
          backdropFilter: 'blur(20px)',
          border: 'none',
        },
      }}
    >
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          <Fade in={open} timeout={300}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #1976d2, #9c27b0)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Menu
            </Typography>
          </Fade>
          <IconButton
            onClick={onClose}
            sx={{
              color: 'text.primary',
              transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease-in-out',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* User Section */}
        {isUserAuthenticated && (
          <Slide direction="left" in={open} timeout={400}>
            <Box
              sx={{
                p: 3,
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <Avatar
                src={user?.image || user?.avatar_url}
                sx={{
                  width: 50,
                  height: 50,
                  border: '2px solid',
                  borderColor: 'primary.main',
                }}
              >
                {user?.name?.[0] || user?.login?.[0] || 'U'}
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {user?.name || user?.login || 'User'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.email || 'Welcome back!'}
                </Typography>
              </Box>
            </Box>
          </Slide>
        )}

        {/* Authentication Section for Non-authenticated Users */}
        {!isUserAuthenticated && (
          <Slide direction="left" in={open} timeout={400}>
            <Box
              sx={{
                p: 3,
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                Sign In
              </Typography>
              <ToolpadAccountComponent variant="default" />
            </Box>
          </Slide>
        )}

        {/* Navigation Items */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          <List>
            {menuItems.map((item, index) => (
              <Slide
                key={item.id}
                direction="left"
                in={open}
                timeout={500 + index * 100}
              >
                <ListItem disablePadding sx={{ mb: 1 }}>
                  <ListItemButton
                    onClick={() => handleItemClick(item.path)}
                    sx={{
                      borderRadius: 2,
                      py: 1.5,
                      px: 2,
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.1)',
                        transform: 'translateX(8px)',
                      },
                      transition: 'all 0.3s ease-in-out',
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        color: 'primary.main',
                        minWidth: 40,
                      }}
                    >
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={item.label}
                      primaryTypographyProps={{
                        fontWeight: 'medium',
                        fontSize: '1.1rem',
                      }}
                    />
                  </ListItemButton>
                </ListItem>
              </Slide>
            ))}
          </List>
        </Box>

        {/* Footer */}
        <Slide direction="up" in={open} timeout={600}>
          <Box
            sx={{
              p: 3,
              borderTop: '1px solid rgba(255, 255, 255, 0.1)',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            {isUserAuthenticated && (
              <ToolpadAccountComponent variant="default" />
            )}
          </Box>
        </Slide>
      </Box>
    </Drawer>
  );
};

ModernMobileMenu.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  isAuthenticated: PropTypes.bool,
  authJsAuthenticated: PropTypes.bool,
  user: PropTypes.object,
};

export default ModernMobileMenu;
