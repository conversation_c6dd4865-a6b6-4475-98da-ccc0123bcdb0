import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Avatar,
  Button,
  Fade,
  Slide,
  Collapse,
  TextField,
  InputAdornment,
  Chip,
  Switch,
  FormControlLabel,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  Close as CloseIcon,
  Home as HomeIcon,
  Person as PersonIcon,
  Work as WorkIcon,
  Article as ArticleIcon,
  ContactMail as ContactMailIcon,
  Star as StarIcon,
  Book as BookIcon,
  Settings as SettingsIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Search as SearchIcon,
  History as HistoryIcon,
  Clear as ClearIcon,
  Logout as LogoutIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  Palette as PaletteIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { AppProvider } from '@toolpad/core/AppProvider';
import { SignInPage } from '@toolpad/core/SignInPage';
import ToolpadAccountComponent from './auth/toolpad/ToolpadAccountComponent';
import AuthJsClient from './auth/AuthJsClient';

const ModernMobileMenu = ({
  open,
  onClose,
  isAuthenticated,
  authJsAuthenticated,
  user,
  isDarkMode,
  toggleDarkMode,
  availablePalettes,
  currentPaletteIndex,
  changePalette,
  resumeItems = [],
  domainKnowledgeData = {},
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [animationDelay, setAnimationDelay] = useState(0);
  const [expandedMenus, setExpandedMenus] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [recentItems, setRecentItems] = useState([]);

  useEffect(() => {
    if (open) {
      setAnimationDelay(100);
      // Load recent items from localStorage
      try {
        const saved = localStorage.getItem('recentItems');
        if (saved) {
          setRecentItems(JSON.parse(saved));
        }
      } catch (error) {
        console.error('Error loading recent items:', error);
      }
    } else {
      setAnimationDelay(0);
    }
  }, [open]);

  // Enhanced menu items with submenus
  const menuItems = useMemo(() => [
    { id: 'home', label: 'Home', icon: <HomeIcon />, path: '/' },
    { id: 'hero', label: 'Hero', icon: <StarIcon />, path: '/heroref' },
    { id: 'about', label: 'About Me', icon: <PersonIcon />, path: '/about' },
    {
      id: 'resume',
      label: 'Resume',
      icon: <WorkIcon />,
      hasSubmenu: true,
      submenu: resumeItems.map(item => ({
        id: item.id,
        label: item.label,
        icon: item.icon || <WorkIcon />,
        path: `/resume#${item.id}`
      }))
    },
    { id: 'portfolio', label: 'Portfolio', icon: <WorkIcon />, path: '/works' },
    { id: 'blog', label: 'Blog', icon: <ArticleIcon />, path: '/blogs' },
    { id: 'contact', label: 'Contact', icon: <ContactMailIcon />, path: '/contact' },
    { id: 'docs', label: 'Documentation', icon: <BookIcon />, path: '/docs' },
  ], [resumeItems]);

  // All searchable items including submenus
  const allSearchableItems = useMemo(() => {
    const items = [];
    menuItems.forEach(item => {
      if (item.hasSubmenu) {
        items.push(...item.submenu);
      } else {
        items.push(item);
      }
    });

    // Add domain knowledge items
    if (domainKnowledgeData?.categories) {
      domainKnowledgeData.categories.forEach(category => {
        items.push({
          id: `domain-${category.id}`,
          label: category.name,
          icon: <ArticleIcon />,
          path: `/knowledge/domain/${category.id}`,
          keywords: ['knowledge', 'domain']
        });

        category.topics?.forEach(topic => {
          items.push({
            id: `domain-${category.id}-${topic.id}`,
            label: topic.title,
            icon: <ArticleIcon />,
            path: `/knowledge/domain/${category.id}/${topic.id}`,
            keywords: ['knowledge', 'domain', category.name.toLowerCase()]
          });
        });
      });
    }

    return items;
  }, [menuItems, domainKnowledgeData]);

  // Filtered search results
  const searchResults = useMemo(() => {
    if (!searchTerm) return [];

    const searchLower = searchTerm.toLowerCase();
    return allSearchableItems.filter(item =>
      item.label.toLowerCase().includes(searchLower) ||
      (item.keywords && item.keywords.some(keyword =>
        keyword.toLowerCase().includes(searchLower)
      ))
    );
  }, [searchTerm, allSearchableItems]);

  const handleItemClick = (item) => {
    // Add to recent items
    const newRecentItem = {
      id: item.id,
      label: item.label,
      path: item.path,
      timestamp: Date.now()
    };

    const updatedRecent = [newRecentItem, ...recentItems.filter(r => r.id !== item.id)].slice(0, 5);
    setRecentItems(updatedRecent);
    localStorage.setItem('recentItems', JSON.stringify(updatedRecent));

    onClose();

    // Handle hash navigation for resume items
    if (item.path.includes('#')) {
      const [path, hash] = item.path.split('#');
      navigate(path);
      setTimeout(() => {
        const element = document.getElementById(hash);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } else {
      navigate(item.path);
    }
  };

  const handleMenuToggle = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const handleSignOut = async () => {
    try {
      await AuthJsClient.signOut();
      onClose();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const clearRecentItems = () => {
    setRecentItems([]);
    localStorage.removeItem('recentItems');
  };

  const isUserAuthenticated = isAuthenticated || authJsAuthenticated;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: '100vw',
          height: '100vh',
          background: 'linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(156, 39, 176, 0.1) 100%)',
          backdropFilter: 'blur(20px)',
          border: 'none',
        },
      }}
    >
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 3,
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          <Fade in={open} timeout={300}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 'bold',
                background: 'linear-gradient(45deg, #1976d2, #9c27b0)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              Menu
            </Typography>
          </Fade>
          <IconButton
            onClick={onClose}
            sx={{
              color: 'text.primary',
              transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease-in-out',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        {/* User Section */}
        {isUserAuthenticated && (
          <Slide direction="left" in={open} timeout={400}>
            <Box
              sx={{
                p: 3,
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: 2,
              }}
            >
              <Avatar
                src={user?.image || user?.avatar_url}
                sx={{
                  width: 50,
                  height: 50,
                  border: '2px solid',
                  borderColor: 'primary.main',
                }}
              >
                {user?.name?.[0] || user?.login?.[0] || 'U'}
              </Avatar>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {user?.name || user?.login || 'User'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user?.email || 'Welcome back!'}
                </Typography>
              </Box>
            </Box>
          </Slide>
        )}

        {/* Authentication Section for Non-authenticated Users */}
        {!isUserAuthenticated && (
          <Slide direction="left" in={open} timeout={400}>
            <Box
              sx={{
                p: 3,
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
              }}
            >
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                Sign In
              </Typography>
              <AppProvider>
                <SignInPage
                  providers={[
                    { id: "github", name: "GitHub" },
                    { id: "google", name: "Google" },
                    { id: "facebook", name: "Facebook" },
                    { id: "linkedin", name: "LinkedIn" },
                    { id: "auth0", name: "Auth0" },
                  ]}
                  signIn={async (provider) => {
                    if (provider && provider.id) {
                      console.log(`[Mobile Menu] Signing in with ${provider.id}`);
                      try {
                        await AuthJsClient.signIn(provider.id);
                        onClose();
                      } catch (error) {
                        console.error(`[Mobile Menu] Error signing in with ${provider.id}:`, error);
                      }
                    }
                  }}
                />
              </AppProvider>
            </Box>
          </Slide>
        )}

        {/* Search Section */}
        <Slide direction="left" in={open} timeout={500}>
          <Box sx={{ p: 2, borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchTerm('')}>
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  },
                },
              }}
            />

            {/* Search Results */}
            {searchTerm && searchResults.length > 0 && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </Typography>
                <List dense>
                  {searchResults.slice(0, 5).map((item) => (
                    <ListItem key={item.id} disablePadding>
                      <ListItemButton
                        onClick={() => handleItemClick(item)}
                        sx={{
                          borderRadius: 1,
                          '&:hover': {
                            backgroundColor: 'rgba(25, 118, 210, 0.1)',
                          },
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={item.label}
                          primaryTypographyProps={{ fontSize: '0.9rem' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Box>
            )}
          </Box>
        </Slide>

        {/* Recent Items */}
        {recentItems.length > 0 && (
          <Slide direction="left" in={open} timeout={600}>
            <Box sx={{ p: 2, borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <HistoryIcon fontSize="small" />
                  <Typography variant="subtitle2" fontWeight="bold">
                    Recent
                  </Typography>
                </Box>
                <Chip
                  label="Clear"
                  size="small"
                  variant="outlined"
                  onClick={clearRecentItems}
                  sx={{ height: 20, fontSize: '0.7rem' }}
                />
              </Box>
              <List dense>
                {recentItems.slice(0, 3).map((item) => (
                  <ListItem key={`recent-${item.id}`} disablePadding>
                    <ListItemButton
                      onClick={() => handleItemClick(item)}
                      sx={{
                        borderRadius: 1,
                        '&:hover': {
                          backgroundColor: 'rgba(25, 118, 210, 0.1)',
                        },
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        <HistoryIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText
                        primary={item.label}
                        primaryTypographyProps={{ fontSize: '0.9rem' }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Box>
          </Slide>
        )}

        {/* Navigation Items */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          <List>
            {menuItems.map((item, index) => (
              <Slide
                key={item.id}
                direction="left"
                in={open}
                timeout={500 + index * 100}
              >
                <Box>
                  <ListItem disablePadding sx={{ mb: 1 }}>
                    <ListItemButton
                      onClick={() => item.hasSubmenu ? handleMenuToggle(item.id) : handleItemClick(item)}
                      sx={{
                        borderRadius: 2,
                        py: 1.5,
                        px: 2,
                        '&:hover': {
                          backgroundColor: 'rgba(25, 118, 210, 0.1)',
                          transform: 'translateX(8px)',
                        },
                        transition: 'all 0.3s ease-in-out',
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          color: 'primary.main',
                          minWidth: 40,
                        }}
                      >
                        {item.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.label}
                        primaryTypographyProps={{
                          fontWeight: 'medium',
                          fontSize: '1.1rem',
                        }}
                      />
                      {item.hasSubmenu && (
                        <IconButton size="small">
                          {expandedMenus[item.id] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      )}
                    </ListItemButton>
                  </ListItem>

                  {/* Submenu */}
                  {item.hasSubmenu && (
                    <Collapse in={expandedMenus[item.id]} timeout="auto" unmountOnExit>
                      <List component="div" disablePadding>
                        {item.submenu.map((subItem) => (
                          <ListItem key={subItem.id} disablePadding sx={{ pl: 4 }}>
                            <ListItemButton
                              onClick={() => handleItemClick(subItem)}
                              sx={{
                                borderRadius: 1,
                                py: 1,
                                px: 2,
                                '&:hover': {
                                  backgroundColor: 'rgba(25, 118, 210, 0.05)',
                                  transform: 'translateX(4px)',
                                },
                                transition: 'all 0.2s ease-in-out',
                              }}
                            >
                              <ListItemIcon sx={{ minWidth: 32, color: 'text.secondary' }}>
                                {subItem.icon}
                              </ListItemIcon>
                              <ListItemText
                                primary={subItem.label}
                                primaryTypographyProps={{
                                  fontSize: '0.95rem',
                                  color: 'text.secondary',
                                }}
                              />
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    </Collapse>
                  )}
                </Box>
              </Slide>
            ))}
          </List>
        </Box>

        {/* Settings & Footer */}
        <Slide direction="up" in={open} timeout={600}>
          <Box
            sx={{
              borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            }}
          >
            {/* Theme Controls */}
            <Box sx={{ p: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold' }}>
                Settings
              </Typography>

              {/* Dark Mode Toggle */}
              <FormControlLabel
                control={
                  <Switch
                    checked={isDarkMode}
                    onChange={(e) => toggleDarkMode(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {isDarkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
                    {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                  </Box>
                }
                sx={{ mb: 2 }}
              />

              {/* Color Palette */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Color Theme
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {availablePalettes?.map((palette, index) => (
                    <Tooltip key={palette.name} title={palette.name} arrow>
                      <Box
                        onClick={() => changePalette(index)}
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          bgcolor: palette.primary,
                          border: '2px solid',
                          borderColor: index === currentPaletteIndex ? 'primary.main' : 'transparent',
                          cursor: 'pointer',
                          transition: 'transform 0.2s',
                          '&:hover': {
                            transform: 'scale(1.2)',
                          },
                        }}
                      />
                    </Tooltip>
                  ))}
                </Box>
              </Box>
            </Box>

            {/* User Actions */}
            {isUserAuthenticated && (
              <Box sx={{ p: 2, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
                <List dense>
                  <ListItem disablePadding>
                    <ListItemButton
                      onClick={() => {
                        onClose();
                        navigate('/profile');
                      }}
                      sx={{ borderRadius: 1 }}
                    >
                      <ListItemIcon>
                        <PersonIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Profile" />
                    </ListItemButton>
                  </ListItem>
                  <ListItem disablePadding>
                    <ListItemButton
                      onClick={handleSignOut}
                      sx={{ borderRadius: 1 }}
                    >
                      <ListItemIcon>
                        <LogoutIcon fontSize="small" />
                      </ListItemIcon>
                      <ListItemText primary="Sign Out" />
                    </ListItemButton>
                  </ListItem>
                </List>
              </Box>
            )}
          </Box>
        </Slide>
      </Box>
    </Drawer>
  );
};

ModernMobileMenu.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  isAuthenticated: PropTypes.bool,
  authJsAuthenticated: PropTypes.bool,
  user: PropTypes.object,
  isDarkMode: PropTypes.bool,
  toggleDarkMode: PropTypes.func,
  availablePalettes: PropTypes.array,
  currentPaletteIndex: PropTypes.number,
  changePalette: PropTypes.func,
  resumeItems: PropTypes.array,
  domainKnowledgeData: PropTypes.object,
};

export default ModernMobileMenu;
